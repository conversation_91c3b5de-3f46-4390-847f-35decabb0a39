/*
Theme Name: DmrThema
Author: Cline
Description: DmrThem<PERSON> i<PERSON><PERSON> o<PERSON>ak geliştirilmiş bir te<PERSON>.
Version: 1.0
*/

html {
    height: 100%;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
}

.container {
    width: 75%;
    margin: 0 auto;
}

/* Header Stilleri */
.site-header {
    border-bottom: 1px solid #e0e0e0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.logo a {
    text-decoration: none;
    color: #ff6000;
    font-size: 28px;
    font-weight: bold;
}

.search-form-container {
    flex-grow: 1;
    margin: 0 30px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-form {
    display: flex;
    width: 100%;
}

.search-form label {
    flex-grow: 1;
}

.search-field {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 5px 0 0 5px;
}

.search-submit {
    padding: 15px 20px;
    border: 1px solid #ff6000;
    background-color: #ff6000;
    color: white;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
}

.user-actions a, .location, .cart a {
    text-decoration: none;
    color: #333;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.user-actions a span {
    font-size: 12px;
    color: #666;
    display: block;
    margin-top: 2px;
}

/* User Avatar ve Dropdown Stilleri */
.user-profile-dropdown {
    position: relative;
    display: inline-block;
}

.user-avatar-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    border-radius: 50%;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-avatar-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.user-avatar-button .user-avatar {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: block;
    object-fit: cover;
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

.user-dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.user-name {
    display: block;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    margin-bottom: 4px;
}

.user-email {
    display: block;
    color: #666;
    font-size: 12px;
}

.user-menu-items {
    padding: 8px 0;
}

.user-menu-item {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.user-menu-item:hover {
    background-color: #f8f9fa;
    color: #ff6000;
}

.user-menu-item.logout {
    border-top: 1px solid #f0f0f0;
    color: #dc3545;
}

.user-menu-item.logout:hover {
    background-color: #fff5f5;
    color: #dc3545;
}

/* Sepet Butonu Stilleri */
.cart-button {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
}

.cart-button:hover {
    background-color: #e0e0e0;
    border-color: #999;
}

.cart-icon {
    width: 20px;
    height: 20px;
}

.cart-count {
    background-color: #ff6000;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.header-bottom {
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.header-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.main-navigation ul {
    display: flex;
    justify-content: flex-start; /* Elemanları sola hizalar */
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation ul li {
    margin-right: 20px;
}

.main-navigation ul li a {
    display: block;
    padding: 20px 15px;
    text-decoration: none;
    color: #333;
    font-weight: bold;
}

.main-navigation ul li a:hover {
    background-color: #e0e0e0;
}

/* Mega Menü Stilleri */
.main-navigation ul li.has-mega-menu {
    position: static; /* Konumlandırmayı header'a göre yapmak için */
}

.main-navigation ul .sub-menu {
    display: none;
    position: absolute;
    left: 0; /* Tam genişlik için */
    right: 0; /* Tam genişlik için */
    width: auto; /* Sol ve sağa göre otomatik genişlik */
    top: auto; /* Üst konumu header'a göre ayarlar */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px 0;
    z-index: 500; /* Slider'ın üstünde görünmesi için */
}

.main-navigation ul li:hover > .sub-menu {
    display: block;
}

.sub-menu .sub-menu-container {
    width: 75%; /* .container ile aynı genişlik */
    margin: 0 auto;
    display: block; /* Flex yerine block */
}

.sub-menu .sub-menu-container > li {
    width: 100%; /* Tek sütun için tam genişlik */
    padding-right: 0;
    box-sizing: border-box;
}

.sub-menu li {
    width: 100%;
}

.sub-menu ul li a {
    padding: 8px 0 !important;
    font-weight: normal !important;
    color: #666 !important;
}

.sub-menu ul li a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.sub-menu .sub-menu-container > li > a {
    font-weight: bold !important;
    color: #333 !important;
}

.sub-menu .sub-menu-container > li ul {
    list-style: none;
    padding-left: 0;
}

/* Renkli Şerit */
.header-bottom::before {
    content: '';
    display: block;
    height: 4px;
    background: linear-gradient(to right, #ff6000 20%, #41b6e6 20%, #41b6e6 40%, #8a3ffc 40%, #8a3ffc 60%, #4caf50 60%, #4caf50 80%, #5e35b1 80%);
}

/* Sepet Sidebar Overlay */
.cart-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999998;
}

.cart-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sepet Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    z-index: 1000000;
    transition: right 0.3s ease;
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-content {
    background-color: white;
    height: 100%;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden; /* Footer tasmasini onle */
}

.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-sidebar-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.cart-sidebar-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #666;
    transition: color 0.3s ease;
}

.cart-sidebar-close:hover {
    color: #333;
}

.cart-sidebar-body {
    flex: 1;
    overflow: hidden; /* Scroll widget_shopping_cart_content'te olacak */
    padding: 20px;
    padding-bottom: 0;
}

.cart-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    margin-top: auto;
}

.cart-footer-info {
    text-align: center;
}

.cart-footer-info p {
    margin: 0;
    color: #666;
}

.cart-footer-info small {
    font-size: 12px;
}

/* Mini Cart Stilleri */
.widget_shopping_cart_content {
    font-size: 14px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.widget_shopping_cart_content .woocommerce-mini-cart {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 180px !important; /* Footer icin yeterli alan birak */
}

.widget_shopping_cart_content .woocommerce-mini-cart-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0 !important;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    z-index: 1;
}

.widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none;
}

/* Urun resimleri - tutarli boyut */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
    width: 50px !important;
    height: 50px !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

/* Remove butonu - tutarli pozisyon */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove {
    position: absolute !important;
    top: 10px !important;
    right: 0 !important;
    color: #ff4444 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    font-size: 16px !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    background: rgba(255, 68, 68, 0.1) !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove:hover {
    color: #cc0000 !important;
    background: rgba(204, 0, 0, 0.1) !important;
}

/* Urun bilgileri - tutarli gorunum */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Cart Footer Stilleri - Sidebar icinde fixed */
.widget_shopping_cart_content .cart-footer {
    position: absolute;
    bottom: 0;
    left: -20px;
    right: -20px;
    padding: 20px;
    background-color: #fff;
    border-top: 2px solid #e9ecef;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100; /* Urunlerin ustunde ama sidebar icinde */
}

/* Toplam fiyat */
.widget_shopping_cart_content .woocommerce-mini-cart__total {
    padding: 0 0 15px 0;
    margin: 0;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__total .amount {
    color: #ff6000;
    font-size: 18px;
}

/* Butonlar */
.widget_shopping_cart_content .woocommerce-mini-cart__buttons {
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button {
    display: block;
    width: 90%;
    text-align: center;
    padding: 12px 15px;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward {
    background-color: #fff;
    color: #333;
    border: 1px solid #dee2e6;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout {
    background-color: #ff6000;
    color: white;
    border: 1px solid #ff6000;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout:hover {
    background-color: #e55a00;
    border-color: #e55a00;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 96, 0, 0.3);
}

/* Bos sepet mesaji */
.widget_shopping_cart_content .woocommerce-mini-cart__empty-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 0;
    border: 1px solid #e9ecef;
}

/* SIDEBAR TUTARLILIGI - TUM SAYFALARDA AYNI GORUNUM */
/* En yuksek oncelik ile tum CSS cakismalarini onle */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.home .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Mini cart listesi icin tutarli stil */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart {
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
}

/* Son elementin border'ini kaldir */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100vw;
        right: -100vw;
    }

    .cart-sidebar-content {
        width: 100%;
    }

    .cart-sidebar-body {
        padding: 15px;
        padding-bottom: 180px; /* Mobilde daha az alan */
    }

    .cart-sidebar-footer {
        padding: 15px;
    }

    .widget_shopping_cart_content .cart-footer {
        left: -15px;
        right: -15px;
        padding: 15px;
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
        width: 40px !important;
        height: 40px !important;
        margin-right: 10px !important;
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
        padding: 12px 0 !important;
    }

    /* User Avatar Mobil Stilleri */
    .user-avatar-button .user-avatar {
        width: 35px;
        height: 35px;
    }

    .user-dropdown-menu {
        right: -10px;
        min-width: 180px;
    }

    .user-info {
        padding: 12px;
    }

    .user-name {
        font-size: 13px;
    }

    .user-email {
        font-size: 11px;
    }

    .user-menu-item {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Footer Stilleri */
.site-footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 40px 0;
    margin-top: 40px;
}

.footer-widgets {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.widget-area {
    width: 30%;
}

.widget-area h3 {
    color: #fff;
    border-bottom: 2px solid #ff6000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.widget-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget-area ul li a {
    color: #ecf0f1;
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s;
}

.widget-area ul li a:hover {
    color: #ff6000;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #34495e;
    padding-top: 20px;
}

.footer-bottom p {
    margin: 0;
    color: #bdc3c7;
}

/* Slider Stilleri */
.main-slider {
    width: 100%;
    height: 500px; /* Yüksekliği isteğe göre ayarlayabilirsiniz */
    margin-bottom: 40px;
    margin-top: 0; /* Header ile boşluğu kaldırmak için */
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* WooCommerce Temel Stiller */
/* Magaza sayfasi icin */
.woocommerce.archive .content-area,
.woocommerce.shop .content-area,
.woocommerce.product-category .content-area,
.woocommerce.product-tag .content-area {
    width: 74% !important;
    margin-left: 230px !important;
}

/* Tekli urun sayfasi icin */
.woocommerce.single-product .content-area {
    width: 74% !important;
    margin-left: 185px !important;
}

.woocommerce .site-main,
.woocommerce-page .site-main {
    margin: 0;
}

/* WooCommerce Ürün Grid - 4lü Grid */
.woocommerce ul.products {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.woocommerce ul.products li.product {
    width: auto !important;
    margin: 0 !important;
    float: none !important;
    clear: none !important;
    display: block !important;
}

/* WooCommerce pseudo-elementleri temizle */
.woocommerce .products ul::after,
.woocommerce .products ul::before,
.woocommerce ul.products::after,
.woocommerce ul.products::before {
    content: none !important;
    display: none !important;
}

/* Alttaki sonuc sayisini ve sıralama butonunu gizle - spesifik elementler */
#main > div.columns-3 > div > p,
#main > div.columns-3 > div > form {
    display: none !important;
}

/* Ürün Detay Sayfası */
.product-details-wrapper {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.product .images {
    flex: 1;
}

.product .summary {
    flex: 1;
}

@media (max-width: 768px) {
    .product-details-wrapper {
        flex-direction: column;
        gap: 20px;
    }
}

/* WooCommerce Sepetim Butonlarini Gizle */
/* Sepete ekleme sonrasi cikan "Sepetim" butonlarini gizler */
.woocommerce ul.products li.product .added_to_cart,
.woocommerce-page ul.products li.product .added_to_cart,
.added_to_cart.wc-forward {
    display: none !important;
}

/* Header Responsive Stilleri */
@media (max-width: 768px) {
    .header-top .container {
        flex-direction: column;
        gap: 15px;
        padding: 20px 0;
    }

    .search-form-container {
        order: 3;
        margin: 0;
        width: 100%;
    }

    .header-right {
        order: 2;
        gap: 15px;
        width: 100%;
        justify-content: space-between;
    }

    .logo {
        order: 1;
    }

    .location {
        display: none; /* Mobilde konum gizle */
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 10px;
    }

    .user-actions .login-button {
        padding: 8px 12px;
        font-size: 13px;
    }

    .user-actions .login-button span {
        display: none; /* Mobilde "veya uye ol" metnini gizle */
    }
}
